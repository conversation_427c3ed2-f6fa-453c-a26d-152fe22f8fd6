/**
 * Chat Manager
 * Manages chat functionality, sessions, and Pro feature integration
 */
import { BaseManager } from '../core/BaseManager.js';

export class ChatManager extends BaseManager {
    constructor(controller) {
        super(controller);
        this.currentSession = null;
        this.isStreaming = false;
        this.streamController = null;
        this.chatHistory = [];
        this.settings = {
            maxHistorySize: 50,
            autoScroll: true,
            showToolExecution: true
        };
    }

    /**
     * Initialize the chat manager
     */
    async init() {
        await super.init();
        await this.loadSettings();
        await this.loadChatHistory();
        this.setupChatUI();
        console.log('ChatManager initialized');
    }

    /**
     * Load chat settings from storage
     */
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['agent_hustle_chat_settings']);
            if (result.agent_hustle_chat_settings) {
                this.settings = { ...this.settings, ...result.agent_hustle_chat_settings };
            }
        } catch (error) {
            this.handleError(error, 'Loading chat settings');
        }
    }

    /**
     * Save chat settings to storage
     */
    async saveSettings() {
        try {
            await chrome.storage.local.set({ agent_hustle_chat_settings: this.settings });
        } catch (error) {
            this.handleError(error, 'Saving chat settings');
        }
    }

    /**
     * Load chat history from storage
     */
    async loadChatHistory() {
        try {
            const result = await chrome.storage.local.get(['agent_hustle_chat_history']);
            this.chatHistory = result.agent_hustle_chat_history || [];
        } catch (error) {
            this.handleError(error, 'Loading chat history');
        }
    }

    /**
     * Save chat history to storage
     */
    async saveChatHistory() {
        try {
            await chrome.storage.local.set({ agent_hustle_chat_history: this.chatHistory });
        } catch (error) {
            this.handleError(error, 'Saving chat history');
        }
    }

    /**
     * Setup chat UI elements
     */
    setupChatUI() {
        // This will be called after the chat section is added to the DOM
        // Initial setup for chat interface
    }

    /**
     * Start a new chat session
     */
    async startNewSession() {
        // PATTERN: Always validate Pro status first
        const proStatus = await this.controller.checkProStatus();
        if (!proStatus.isPro) {
            this.controller.uiManager.showError('🚫 Agent Hustle Chat is a Pro feature. Please upgrade to access unlimited AI conversations.');
            this.controller.navigateToSection('upgradeSection');
            return null;
        }

        this.currentSession = {
            id: `chat-${Date.now()}`,
            title: 'New Chat',
            messages: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isPro: true
        };

        // Add to history
        this.chatHistory.unshift(this.currentSession);
        await this.saveChatHistory();

        return this.currentSession;
    }

    /**
     * Send a message in the current chat session
     */
    async sendMessage(content) {
        try {
            // PATTERN: Always validate Pro status first
            const proStatus = await this.controller.checkProStatus();
            if (!proStatus.isPro) {
                this.controller.uiManager.showError('🚫 Agent Hustle Chat is a Pro feature. Please upgrade to continue your conversation.');
                this.controller.navigateToSection('upgradeSection');
                return;
            }

            // Ensure we have a session
            if (!this.currentSession) {
                await this.startNewSession();
            }

            // Create user message
            const userMessage = {
                id: `msg-${Date.now()}`,
                role: 'user',
                content: content,
                timestamp: new Date().toISOString(),
                tools: []
            };

            // Add user message to session
            this.currentSession.messages.push(userMessage);
            this.currentSession.updatedAt = new Date().toISOString();
            
            // Update UI immediately
            this.addMessageToUI(userMessage);

            // Send to background for processing
            this.isStreaming = true;
            const response = await chrome.runtime.sendMessage({
                action: 'sendChatMessage',
                data: { 
                    message: content, 
                    sessionId: this.currentSession.id,
                    messages: this.currentSession.messages
                }
            });

            if (response.error) {
                throw new Error(response.error);
            }

            // Handle streaming response
            await this.handleStreamingResponse(response);

        } catch (error) {
            this.handleError(error, 'Sending message');
            this.isStreaming = false;
        }
    }

    /**
     * Handle streaming response from background script
     */
    async handleStreamingResponse(response) {
        // This will be implemented when we add the streaming handler
        // For now, handle as a complete response
        if (response.result && response.result.content) {
            const assistantMessage = {
                id: `msg-${Date.now()}`,
                role: 'assistant',
                content: response.result.content,
                timestamp: new Date().toISOString(),
                tools: []
            };

            this.currentSession.messages.push(assistantMessage);
            this.currentSession.updatedAt = new Date().toISOString();
            
            this.addMessageToUI(assistantMessage);
            await this.saveChatHistory();
        }
        
        this.isStreaming = false;
    }

    /**
     * Add a message to the UI
     */
    addMessageToUI(message) {
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;

        const messageElement = document.createElement('div');
        messageElement.className = `message ${message.role}`;
        messageElement.dataset.messageId = message.id;

        const time = new Date(message.timestamp).toLocaleTimeString();
        
        messageElement.innerHTML = `
            <div class="message-header">
                <span class="message-role">${message.role === 'user' ? 'You' : 'Agent Hustle'}</span>
                <span class="message-time">${time}</span>
            </div>
            <div class="message-content">${this.formatMessageContent(message.content)}</div>
        `;

        chatMessages.appendChild(messageElement);
        
        // Auto-scroll if enabled
        if (this.settings.autoScroll) {
            this.scrollToBottom();
        }
    }

    /**
     * Format message content for display
     */
    formatMessageContent(content) {
        // Basic markdown-like formatting
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    /**
     * Scroll chat to bottom
     */
    scrollToBottom() {
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }

    /**
     * Load a specific chat session
     */
    async loadSession(sessionId) {
        // PATTERN: Always validate Pro status first
        const proStatus = await this.controller.checkProStatus();
        if (!proStatus.isPro) {
            this.controller.uiManager.showError('🚫 Chat history access requires a Pro subscription. Please upgrade to view your conversations.');
            this.controller.navigateToSection('upgradeSection');
            return;
        }

        const session = this.chatHistory.find(s => s.id === sessionId);
        if (!session) {
            this.handleError(new Error('Session not found'), 'Loading session');
            return;
        }

        this.currentSession = session;
        this.renderChatSession();
    }

    /**
     * Render the current chat session in the UI
     */
    renderChatSession() {
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages || !this.currentSession) return;

        // Clear existing messages
        chatMessages.innerHTML = '';

        // Add all messages from session
        this.currentSession.messages.forEach(message => {
            this.addMessageToUI(message);
        });
    }

    /**
     * Delete a chat session
     */
    async deleteSession(sessionId) {
        try {
            this.chatHistory = this.chatHistory.filter(s => s.id !== sessionId);
            await this.saveChatHistory();
            
            // If this was the current session, clear it
            if (this.currentSession && this.currentSession.id === sessionId) {
                this.currentSession = null;
                const chatMessages = document.getElementById('chatMessages');
                if (chatMessages) {
                    chatMessages.innerHTML = '';
                }
            }
            
            this.handleSuccess('Chat session deleted');
        } catch (error) {
            this.handleError(error, 'Deleting session');
        }
    }

    /**
     * Clear all chat history
     */
    async clearAllHistory() {
        try {
            this.chatHistory = [];
            this.currentSession = null;
            await this.saveChatHistory();
            
            const chatMessages = document.getElementById('chatMessages');
            if (chatMessages) {
                chatMessages.innerHTML = '';
            }
            
            this.handleSuccess('All chat history cleared');
        } catch (error) {
            this.handleError(error, 'Clearing history');
        }
    }

    /**
     * Get chat statistics
     */
    getChatStats() {
        return {
            totalSessions: this.chatHistory.length,
            totalMessages: this.chatHistory.reduce((sum, session) => sum + session.messages.length, 0),
            currentSessionMessages: this.currentSession ? this.currentSession.messages.length : 0
        };
    }

    /**
     * Export chat history
     */
    async exportHistory() {
        try {
            // PATTERN: Always validate Pro status first
            const proStatus = await this.controller.checkProStatus();
            if (!proStatus.isPro) {
                this.controller.uiManager.showError('🚫 Chat history export is a Pro feature. Please upgrade to export your conversations.');
                this.controller.navigateToSection('upgradeSection');
                return;
            }

            const exportData = {
                exportDate: new Date().toISOString(),
                totalSessions: this.chatHistory.length,
                sessions: this.chatHistory
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent-hustle-chat-history-${Date.now()}.json`;
            
            document.body.appendChild(a);
            a.click();
            
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.handleSuccess('Chat history exported successfully');
        } catch (error) {
            this.handleError(error, 'Exporting history');
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        this.isStreaming = false;
        this.streamController = null;
        this.currentSession = null;
    }
}