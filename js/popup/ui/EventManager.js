/**
 * Event Manager
 * Handles all event listeners and delegation for the popup
 */
import { BaseManager } from '../core/BaseManager.js';

export class EventManager extends BaseManager {
    constructor(controller) {
        super(controller);
        this.eventListeners = new Map(); // Track event listeners for cleanup
    }

    async init() {
        await super.init();
        this.setupEventListeners();
        this.setupMessageListeners();
        this.setupEventDelegation();
    }

    /**
     * Add event listener with tracking for cleanup
     */
    addEventListenerTracked(element, event, handler, options = {}) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        
        if (!element) {
            console.warn(`Element not found for event listener: ${element}`);
            return;
        }

        element.addEventListener(event, handler, options);
        
        // Track for cleanup
        const key = `${element.id || 'unknown'}-${event}`;
        this.eventListeners.set(key, { element, event, handler, options });
    }

    /**
     * Setup all main event listeners
     */
    setupEventListeners() {
        // API Key Management
        this.addEventListenerTracked('saveApiKey', 'click', async () => {
            const apiKey = document.getElementById('apiKeyInput').value.trim();
            if (apiKey) {
                await this.controller.settingsManager.saveApiKey(apiKey);
                this.controller.uiManager.showSuccess('API Key saved successfully!');
                
                // Navigate to actions section after successful save
                setTimeout(() => {
                    this.controller.navigateToSection('actionsSection');
                }, 1000); // Small delay to let user see the success message
            } else {
                this.controller.uiManager.showError('Please enter a valid API key');
            }
        });

        // Quick Actions
        this.addEventListenerTracked('analyzeSelection', 'click', () => {
            this.controller.analysisManager.analyzeSelection();
        });

        this.addEventListenerTracked('analyzePage', 'click', () => {
            this.controller.analysisManager.analyzePage();
        });

        this.addEventListenerTracked('customAnalysis', 'click', async () => {
            // Check pro status and navigate accordingly
            await this.controller.settingsManager.handleCustomAnalysisClick();
        });

        this.addEventListenerTracked('viewAnalysisHistory', 'click', () => {
            this.controller.navigateToSection('analysisHistorySection');
            this.controller.dataManager.loadAndDisplayAnalysis();
        });

        this.addEventListenerTracked('managePrompts', 'click', async () => {
            await this.controller.settingsManager.handlePromptManagerClick();
        });

        this.addEventListenerTracked('scrapeAndAnalyze', 'click', async () => {
            await this.controller.settingsManager.handleScrapeAndAnalyzeClick();
        });

        // Chat Actions
        this.addEventListenerTracked('startChat', 'click', async () => {
            // Check pro status before starting chat
            const proStatus = await this.controller.checkProStatus();
            if (!proStatus.isPro) {
                this.controller.navigateToSection('upgradeSection');
                return;
            }
            
            // Navigate to chat section and start new session
            this.controller.navigateToSection('chatSection');
            await this.controller.chatManager.startNewSession();
        });

        // Chat Section Events
        this.addEventListenerTracked('backToActionsFromChat', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('sendChatMessage', 'click', () => {
            this.sendChatMessage();
        });

        this.addEventListenerTracked('newChatSession', 'click', async () => {
            await this.controller.chatManager.startNewSession();
        });

        this.addEventListenerTracked('chatHistory', 'click', () => {
            this.controller.navigateToSection('analysisHistorySection');
            // TODO: Load chat history instead of analysis history
        });

        this.addEventListenerTracked('clearChat', 'click', async () => {
            if (confirm('Are you sure you want to clear the current chat?')) {
                const chatMessages = document.getElementById('chatMessages');
                if (chatMessages) {
                    chatMessages.innerHTML = '';
                }
                this.controller.chatManager.currentSession = null;
            }
        });

        // History Tab Navigation
        this.addEventListenerTracked('analysisHistoryTab', 'click', () => {
            this.switchHistoryTab('analysis');
        });

        this.addEventListenerTracked('chatHistoryTab', 'click', () => {
            this.switchHistoryTab('chat');
        });

        // Chat History Actions
        this.addEventListenerTracked('newChatFromHistory', 'click', async () => {
            // Check pro status before starting chat
            const proStatus = await this.controller.checkProStatus();
            if (!proStatus.isPro) {
                this.controller.navigateToSection('upgradeSection');
                return;
            }
            
            // Navigate to chat section and start new session
            this.controller.navigateToSection('chatSection');
            await this.controller.chatManager.startNewSession();
        });

        this.addEventListenerTracked('exportChatHistory', 'click', async () => {
            await this.controller.chatManager.exportHistory();
        });

        this.addEventListenerTracked('clearAllChatHistory', 'click', async () => {
            if (confirm('Are you sure you want to delete all chat history? This action cannot be undone.')) {
                await this.controller.chatManager.clearAllHistory();
                this.loadChatHistory();
            }
        });

        // Chat input keyboard events
        const chatInput = document.getElementById('chatInput');
        if (chatInput) {
            this.addEventListenerTracked(chatInput, 'keydown', (e) => {
                // Send message with Ctrl+Enter or Cmd+Enter
                if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                    e.preventDefault();
                    this.sendChatMessage();
                }
                
                // Auto-resize textarea
                this.autoResizeChatInput(e.target);
            });

            this.addEventListenerTracked(chatInput, 'input', (e) => {
                this.autoResizeChatInput(e.target);
            });
        }

        // Custom Analysis Form
        this.addEventListenerTracked('backToActions', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('runCustomAnalysis', 'click', () => {
            this.controller.analysisManager.runCustomAnalysis();
        });

        this.addEventListenerTracked('loadSelectedPrompt', 'click', () => {
            this.controller.promptUIManager.loadSelectedPrompt();
        });

        this.addEventListenerTracked('saveCurrentPrompt', 'click', () => {
            this.controller.promptUIManager.saveCurrentPrompt();
        });

        // Scrape Form Events
        this.addEventListenerTracked('backToActionsFromScrape', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('analyzeScrapeUrl', 'click', () => {
            this.controller.analysisManager.analyzeScrapeUrl();
        });

        // Results Section
        this.addEventListenerTracked('backToActionsFromResults', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('copyResults', 'click', () => {
            this.controller.analysisManager.copyResults();
        });

        this.addEventListenerTracked('exportResults', 'click', () => {
            this.controller.analysisManager.exportResults();
        });

        // Navigation buttons
        this.addEventListenerTracked('backToActionsFromHistory', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('backToActionsFromHelp', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('backToActionsFromAbout', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('backToActionsFromSettings', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('backToActionsFromUpgrade', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('backToActionsFromPrompts', 'click', () => {
            this.controller.goBack();
        });

        // Prompt Management Section
        this.addEventListenerTracked('promptSearch', 'input', (e) => {
            this.controller.promptUIManager.searchPrompts(e.target.value);
        });

        this.addEventListenerTracked('promptSort', 'change', (e) => {
            this.controller.promptUIManager.sortPrompts(e.target.value);
        });

        this.addEventListenerTracked('addNewPrompt', 'click', async () => {
            await this.controller.promptUIManager.openPromptEditor();
        });

        this.addEventListenerTracked('exportPrompts', 'click', () => {
            this.controller.promptUIManager.exportPrompts();
        });

        this.addEventListenerTracked('importPrompts', 'click', () => {
            document.getElementById('importPromptsFile').click();
        });

        this.addEventListenerTracked('importPromptsFile', 'change', (e) => {
            this.controller.promptUIManager.importPrompts(e.target.files[0]);
        });

        // Prompt Editor Modal
        this.addEventListenerTracked('closePromptEditor', 'click', () => {
            this.controller.uiManager.closePromptEditor();
        });

        this.addEventListenerTracked('cancelPromptEdit', 'click', () => {
            this.controller.uiManager.closePromptEditor();
        });

        this.addEventListenerTracked('savePromptEdit', 'click', () => {
            this.controller.promptUIManager.savePromptEdit();
        });

        // Close modal when clicking outside
        this.addEventListenerTracked('promptEditorModal', 'click', (e) => {
            if (e.target.id === 'promptEditorModal') {
                this.controller.uiManager.closePromptEditor();
            }
        });

        // Key Management Modal Events
        this.addEventListenerTracked('closeKeyManagement', 'click', () => {
            this.controller.uiManager.closeKeyManagementModal();
        });

        // Close key management modal when clicking outside
        this.addEventListenerTracked('keyManagementModal', 'click', (e) => {
            if (e.target.id === 'keyManagementModal') {
                this.controller.uiManager.closeKeyManagementModal();
            }
        });

        this.addEventListenerTracked('validateProKey', 'click', async () => {
            await this.controller.settingsManager.handleProKeyValidation();
        });

        // Clear pro cache button
        this.addEventListenerTracked('clearProCache', 'click', async () => {
            await this.controller.settingsManager.handleClearProCache();
        });

        // Allow Enter key to validate pro key
        this.addEventListenerTracked('proKeyInput', 'keypress', async (e) => {
            if (e.key === 'Enter') {
                await this.controller.settingsManager.handleProKeyValidation();
            }
        });

        // Data source radio button change handler
        document.addEventListener('change', (e) => {
            if (e.target.name === 'dataSource') {
                const urlInputGroup = document.getElementById('urlInputGroup');
                if (urlInputGroup) {
                    urlInputGroup.style.display = e.target.value === 'url' ? 'block' : 'none';
                }
            }
        });

        // Footer Links
        this.addEventListenerTracked('settingsLink', 'click', (e) => {
            e.preventDefault();
            this.controller.navigateToSection('settingsSection');
        });

        this.addEventListenerTracked('helpLink', 'click', (e) => {
            e.preventDefault();
            this.controller.navigateToSection('helpSection');
        });

        this.addEventListenerTracked('aboutLink', 'click', (e) => {
            e.preventDefault();
            this.controller.navigateToSection('aboutSection');
        });

        // Analysis History List
        this.addEventListenerTracked('analysisHistoryList', 'click', (e) => {
            // Check if delete button was clicked
            const deleteBtn = e.target.closest('.delete-btn');
            if (deleteBtn) {
                e.stopPropagation(); // Prevent card click
                const analysisId = deleteBtn.dataset.analysisId;
                this.controller.dataManager.deleteAnalysis(analysisId);
                return;
            }
            
            // Handle card click for viewing analysis
            const historyItem = e.target.closest('.history-item');
            if (historyItem && historyItem.dataset.analysisId) {
                this.controller.dataManager.viewAnalysisFromHistory(historyItem.dataset.analysisId);
            }
        });
    }

    /**
     * Setup message listeners for communication with content script
     */
    setupMessageListeners() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'triggerAnalysis') {
                console.log('Received triggerAnalysis message:', request.type, request.data);
                
                if (request.type === 'selection' && request.data) {
                    this.controller.analysisManager.analyzeSelectionWithText(request.data);
                } else if (request.type === 'page' && request.data) {
                    this.controller.analysisManager.analyzePageWithData(request.data);
                }
                
                sendResponse({ success: true, message: 'Analysis triggered' });
                return true; // Indicates an asynchronous response
            }
        });
    }

    /**
     * Setup event delegation for dynamically created elements
     */
    setupEventDelegation() {
        // Event delegation for history items
        document.addEventListener('click', (e) => {
            console.log('🔍 Click event detected:', e.target);

            const deleteBtn = e.target.closest('.delete-btn');
            const telegramBtn = e.target.closest('.telegram-send-btn');
            const discordBtn = e.target.closest('.discord-send-btn');
            const viewDetailsLink = e.target.closest('.view-details-link');
            const historyItem = e.target.closest('.history-item');

            // Handle delete button
            if (deleteBtn) {
                e.stopPropagation();
                const analysisId = deleteBtn.dataset.analysisId;
                console.log('🗑️ Delete button clicked for analysis:', analysisId);
                if (analysisId) {
                    this.controller.dataManager.deleteAnalysis(analysisId);
                }
                return;
            }

            // Handle Telegram button
            if (telegramBtn) {
                e.stopPropagation();
                const analysisId = telegramBtn.dataset.analysisId;
                console.log('📱 Telegram button clicked for analysis:', analysisId);
                if (analysisId) {
                    this.controller.integrationManager.sendAnalysisToTelegram(analysisId);
                }
                return;
            }

            // Handle Discord button
            if (discordBtn) {
                e.stopPropagation();
                const analysisId = discordBtn.dataset.analysisId;
                console.log('💬 Discord button clicked for analysis:', analysisId);
                if (analysisId) {
                    this.controller.integrationManager.sendAnalysisToDiscord(analysisId);
                }
                return;
            }

            // Handle view details link
            if (viewDetailsLink) {
                e.stopPropagation();
                const analysisId = viewDetailsLink.dataset.analysisId;
                console.log('👁️ View details clicked for analysis:', analysisId);
                if (analysisId) {
                    this.controller.dataManager.viewAnalysisFromHistory(analysisId);
                }
                return;
            }

            // Handle history item click (only if no other button was clicked)
            if (historyItem && !deleteBtn && !telegramBtn && !discordBtn && !viewDetailsLink) {
                const analysisId = historyItem.dataset.analysisId;
                console.log('📄 History item clicked for analysis:', analysisId);
                if (analysisId) {
                    this.controller.dataManager.viewAnalysisFromHistory(analysisId);
                }
            }
        });

        // Event delegation for prompt card buttons
        document.addEventListener('click', (e) => {
            const button = e.target.closest('.btn-icon[data-action]');
            if (button) {
                e.stopPropagation();
                const action = button.dataset.action;
                const promptId = button.dataset.promptId;

                if (promptId) {
                    switch (action) {
                        case 'use':
                            this.controller.promptUIManager.usePrompt(promptId);
                            break;
                        case 'copy':
                            this.controller.promptUIManager.copyPromptContent(promptId);
                            break;
                        case 'pin':
                            this.controller.promptUIManager.togglePromptPin(promptId);
                            break;
                        case 'edit':
                            this.controller.promptUIManager.editPrompt(promptId);
                            break;
                        case 'delete':
                            this.controller.promptUIManager.deletePromptConfirm(promptId);
                            break;
                    }
                }
            }
        });

        // Event delegation for tag clicks
        document.addEventListener('click', (e) => {
            const tag = e.target.closest('.tag[data-tag]');
            if (tag) {
                e.stopPropagation();
                const tagName = tag.dataset.tag;
                this.controller.promptUIManager.filterByTag(tagName);
            }
        });

        // Event delegation for tag filter buttons
        document.addEventListener('click', (e) => {
            const filterButton = e.target.closest('.tag-filter[data-filter-tag]');
            if (filterButton) {
                e.stopPropagation();
                const tagName = filterButton.dataset.filterTag || null;
                this.controller.promptUIManager.filterByTag(tagName);
            }
        });

        // Enhanced keyboard navigation for prompt cards and tags
        document.addEventListener('keydown', (e) => {
            const tag = e.target.closest('.tag');

            if (tag && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                if (tag.dataset.tag) {
                    this.controller.promptUIManager.filterByTag(tag.dataset.tag);
                }
            }
        });

        // Event delegation for pagination controls
        document.addEventListener('click', (e) => {
            const paginationBtn = e.target.closest('.pagination-btn[data-page]');
            if (paginationBtn && !paginationBtn.disabled) {
                e.stopPropagation();
                const page = parseInt(paginationBtn.dataset.page);
                const paginationContainer = paginationBtn.closest('.pagination-wrapper');

                if (paginationContainer) {
                    // Determine which pagination this belongs to
                    if (paginationContainer.id === 'historyPagination') {
                        this.controller.historyPagination.goToPage(page);
                    } else if (paginationContainer.id === 'promptPagination') {
                        this.controller.promptPagination.goToPage(page);
                    }
                }
            }
        });
    }

    /**
     * Send chat message
     */
    async sendChatMessage() {
        const chatInput = document.getElementById('chatInput');
        if (!chatInput) return;

        const message = chatInput.value.trim();
        if (!message) return;

        try {
            // Disable input while sending
            chatInput.disabled = true;
            const sendButton = document.getElementById('sendChatMessage');
            if (sendButton) {
                sendButton.disabled = true;
            }

            // Clear input immediately for better UX
            chatInput.value = '';
            this.autoResizeChatInput(chatInput);

            // Update status
            const statusText = document.querySelector('.chat-status-text');
            if (statusText) {
                statusText.textContent = 'Sending...';
            }

            // Send message through chat manager
            await this.controller.chatManager.sendMessage(message);

            // Update status
            if (statusText) {
                statusText.textContent = 'Ready to chat';
            }
        } catch (error) {
            console.error('Error sending chat message:', error);
            this.controller.uiManager.showError('Failed to send message');

            // Restore message if send failed
            chatInput.value = message;
            this.autoResizeChatInput(chatInput);
        } finally {
            // Re-enable input
            chatInput.disabled = false;
            const sendButton = document.getElementById('sendChatMessage');
            if (sendButton) {
                sendButton.disabled = false;
            }
            chatInput.focus();
        }
    }

    /**
     * Auto-resize chat input textarea
     */
    autoResizeChatInput(textarea) {
        if (!textarea) return;

        // Reset height to calculate scrollHeight
        textarea.style.height = 'auto';
        
        // Set height based on scrollHeight
        const minHeight = 40; // 2 lines minimum
        const maxHeight = 120; // 6 lines maximum
        const newHeight = Math.min(Math.max(textarea.scrollHeight, minHeight), maxHeight);
        
        textarea.style.height = newHeight + 'px';
        
        // Show/hide scrollbar
        if (textarea.scrollHeight > maxHeight) {
            textarea.style.overflowY = 'auto';
        } else {
            textarea.style.overflowY = 'hidden';
        }
    }

    /**
     * Switch between history tabs
     */
    switchHistoryTab(tabType) {
        // Update tab active states
        const analysisTab = document.getElementById('analysisHistoryTab');
        const chatTab = document.getElementById('chatHistoryTab');
        const analysisContent = document.getElementById('analysisHistoryContent');
        const chatContent = document.getElementById('chatHistoryContent');

        if (!analysisTab || !chatTab || !analysisContent || !chatContent) return;

        // Remove active state from all tabs
        analysisTab.classList.remove('active');
        chatTab.classList.remove('active');
        
        // Hide all content
        analysisContent.style.display = 'none';
        chatContent.style.display = 'none';

        if (tabType === 'analysis') {
            analysisTab.classList.add('active');
            analysisContent.style.display = 'block';
            // Load analysis history (existing functionality)
            this.controller.dataManager.loadAndDisplayAnalysis();
        } else if (tabType === 'chat') {
            chatTab.classList.add('active');
            chatContent.style.display = 'block';
            // Load chat history
            this.loadChatHistory();
        }
    }

    /**
     * Load and display chat history
     */
    async loadChatHistory() {
        try {
            const chatHistoryList = document.getElementById('chatHistoryList');
            if (!chatHistoryList) return;

            // Show loading state
            chatHistoryList.innerHTML = '<div class="loading-state">Loading chat history...</div>';

            // Get chat history from ChatManager
            const chatSessions = await this.controller.chatManager.loadChatHistory();
            
            // Clear loading state
            chatHistoryList.innerHTML = '';

            if (!chatSessions || chatSessions.length === 0) {
                // Show empty state
                chatHistoryList.innerHTML = `
                    <div class="chat-history-empty">
                        <div class="chat-history-empty-icon">💬</div>
                        <div class="chat-history-empty-title">No Chat History</div>
                        <div class="chat-history-empty-desc">Start a conversation with Agent Hustle to see your chat history here.</div>
                        <button id="startFirstChat" class="btn btn-primary">Start Chatting</button>
                    </div>
                `;
                
                // Add event listener for start first chat button
                const startFirstChatBtn = document.getElementById('startFirstChat');
                if (startFirstChatBtn) {
                    startFirstChatBtn.addEventListener('click', async () => {
                        const proStatus = await this.controller.checkProStatus();
                        if (!proStatus.isPro) {
                            this.controller.navigateToSection('upgradeSection');
                            return;
                        }
                        this.controller.navigateToSection('chatSection');
                        await this.controller.chatManager.startNewSession();
                    });
                }
                return;
            }

            // Display chat sessions
            chatSessions.forEach(session => {
                const sessionElement = this.createChatHistoryElement(session);
                chatHistoryList.appendChild(sessionElement);
            });

        } catch (error) {
            console.error('Error loading chat history:', error);
            const chatHistoryList = document.getElementById('chatHistoryList');
            if (chatHistoryList) {
                chatHistoryList.innerHTML = '<div class="error-state">Failed to load chat history</div>';
            }
        }
    }

    /**
     * Create chat history element
     */
    createChatHistoryElement(session) {
        const sessionElement = document.createElement('div');
        sessionElement.className = 'chat-history-item';
        sessionElement.dataset.sessionId = session.id;

        const lastMessage = session.messages[session.messages.length - 1];
        const messageCount = session.messages.length;
        const userMessageCount = session.messages.filter(m => m.role === 'user').length;
        const createdDate = new Date(session.createdAt).toLocaleDateString();
        const updatedDate = new Date(session.updatedAt).toLocaleString();

        // Generate preview from first user message or last assistant message
        const firstUserMessage = session.messages.find(m => m.role === 'user');
        const preview = firstUserMessage ? 
            firstUserMessage.content.substring(0, 100) + (firstUserMessage.content.length > 100 ? '...' : '') :
            'Chat session';

        sessionElement.innerHTML = `
            <div class="chat-history-header">
                <div class="chat-history-title">${session.title}</div>
                <div class="chat-history-date">${createdDate}</div>
            </div>
            <div class="chat-history-preview">${preview}</div>
            <div class="chat-history-meta">
                <div class="chat-history-stats">
                    <div class="chat-history-stat">
                        <span>💬</span>
                        <span>${messageCount} messages</span>
                    </div>
                    <div class="chat-history-stat">
                        <span>👤</span>
                        <span>${userMessageCount} from you</span>
                    </div>
                </div>
                <div class="chat-history-actions-inline">
                    <button class="btn btn-outline btn-sm load-chat-btn" data-session-id="${session.id}">Open</button>
                    <button class="btn btn-outline btn-sm delete-chat-btn" data-session-id="${session.id}">Delete</button>
                </div>
            </div>
        `;

        // Add event listeners
        const loadBtn = sessionElement.querySelector('.load-chat-btn');
        const deleteBtn = sessionElement.querySelector('.delete-chat-btn');

        if (loadBtn) {
            loadBtn.addEventListener('click', async (e) => {
                e.stopPropagation();
                await this.loadChatSession(session.id);
            });
        }

        if (deleteBtn) {
            deleteBtn.addEventListener('click', async (e) => {
                e.stopPropagation();
                if (confirm('Are you sure you want to delete this chat session?')) {
                    await this.controller.chatManager.deleteSession(session.id);
                    this.loadChatHistory(); // Refresh the list
                }
            });
        }

        // Click on the session to load it
        sessionElement.addEventListener('click', async () => {
            await this.loadChatSession(session.id);
        });

        return sessionElement;
    }

    /**
     * Load a specific chat session
     */
    async loadChatSession(sessionId) {
        try {
            // Check pro status
            const proStatus = await this.controller.checkProStatus();
            if (!proStatus.isPro) {
                this.controller.navigateToSection('upgradeSection');
                return;
            }

            // Navigate to chat section
            this.controller.navigateToSection('chatSection');
            
            // Load the specific session
            await this.controller.chatManager.loadSession(sessionId);
        } catch (error) {
            console.error('Error loading chat session:', error);
            this.controller.uiManager.showError('Failed to load chat session');
        }
    }

    /**
     * Handle chat keyboard shortcuts
     */
    handleChatKeyboardShortcuts(event) {
        // Escape to cancel current operation
        if (event.key === 'Escape') {
            const chatInput = document.getElementById('chatInput');
            if (chatInput && chatInput === document.activeElement) {
                chatInput.blur();
                return;
            }
            
            // Cancel streaming if in progress
            if (this.controller.chatManager.isStreaming) {
                this.controller.chatManager.streamHandler?.cancelStreaming();
            }
        }
        
        // Focus chat input with '/'
        if (event.key === '/' && !event.ctrlKey && !event.metaKey) {
            const activeElement = document.activeElement;
            if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA') {
                event.preventDefault();
                const chatInput = document.getElementById('chatInput');
                if (chatInput) {
                    chatInput.focus();
                }
            }
        }
    }

    /**
     * Cleanup all tracked event listeners
     */
    cleanup() {
        for (const [key, { element, event, handler, options }] of this.eventListeners) {
            try {
                element.removeEventListener(event, handler, options);
            } catch (error) {
                console.warn(`Failed to remove event listener ${key}:`, error);
            }
        }
        this.eventListeners.clear();
        super.cleanup();
    }
}
